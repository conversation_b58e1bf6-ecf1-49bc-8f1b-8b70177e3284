nameOverride: scanresult-service
fullnameOverride: scanresult-service
replicaCount: 1

image:
  registry: "205744758777.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-sca-scanresult-service"
  tag: "ATL-3810-316382cf"

imageConfig:
  pullPolicy: IfNotPresent

imagePullSecrets: []
initContainerConfig: {}

service:
  type: ClusterIP
  port: 80
  targetPort: https
  ports:
    - port: 443
      name: https
      protocol: TCP
      targetPort: https

ports: |
  - name: https
    containerPort: 8509
    protocol: TCP
  - name: actuator
    containerPort: 8609
    protocol: TCP

secret:
  - name: sca-scanresult-service-secrets
    type: Opaque
    data:
      KEY_STORE_PASSWORD: ZmtsM24wOW52bHdpMgo=
      TRUST_STORE_PASSWORD: Y2hhbmdlaXQK
      # Spring Boot SSL password environment variables (standard naming convention)
      SERVER_SSL_KEYSTORE_PASSWORD: ZmtsM24wOW52bHdpMgo=
      SERVER_SSL_TRUSTSTORE_PASSWORD: Y2hhbmdlaXQK
      SPRING_DATASOURCE_READWRITEDB_USERNAME: c2NhdXNlcg==
      SPRING_DATASOURCE_READWRITEDB_PASSWORD: c2NhdXNFcjAx
      SPRING_DATASOURCE_READONLYDB_USERNAME: c2NhdXNlcg==
      SPRING_DATASOURCE_READONLYDB_PASSWORD: c2NhdXNFcjAx
  - name: sca-scanresult-service-certs 
    type: Opaque
    data:
      agora-identityservice-principal-signing-1.pem: LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFwRVNaTjJCemRrUE9FMERkc1FUdQp0cnZRMEsxYVpWYmg0cTFrQU1lZU1IUGc1VW1TUDk5T0diR0pyRUFTRU9qcXlvTmtsTS9JSEhjM1M4dm5PbWhNCjlkSm90a3I2Lzh4VkxsRDVweGtjczJRa0hGOGgxZmkxQ1l5VDVYS1JhS1k5VDdLcnE5UitJeWlQYWpFNHJpaEEKTkgvSkNtdks1UGs5OVIzWTFmclVVeXNRaUtHUTFQS0xJZWxjYWZkZS9pL1RBbzBEV0JiYjRZb3RHY1BmRGdZdQoyS1BaN29mR25jRVBReGJiby9LSVJIY2dhNUdrVUNSRTlyWkF0T0hoSjdobW0wUktkSVYvZ1YyUWVKdG0rWlIzClA0YnFaMGVqYWRzU3NrazBScnRwNlpocFZ0QWVjNE15QXNmVDdnVGdyZEFHZU9qTDJTTTRxUkppQUE0aDc4djgKWlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==
      sca-scanresult-signing.pem: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      sca-scanresult-service-keystore.jks: 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
      truststore.jks: 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


env:
  LOGGING_LEVEL_COM_ZAXXER_HIKARI_POOL_HIKARIPOOL: INFO
  LOGGING_LEVEL_DATADOG_TRACE_AGENT_COMMON_WRITER_DDAGENT_DDAGENTAPI: FATAL
  LOGGING_LEVEL_COM_DATADOG_PROFILING_UPLOADER_PROFILEUPLOADER: FATAL
  LOGGING_LEVEL_COM_VERACODE_AGORA_SERVICEBASE_RESTCALLINTERCEPTOR: FATAL
  AGORA_SERVER_SSL_CLIENTS_ALLOWED: '*'
  APPLICATION_JMS_ENABLED: 'false'
  APPLICATION_JMS_DATA_UPDATE_ENABLED: 'false'
  APPLICATION_JMS_DATA_UPDATE_QUEUE: __ENV_BRANCH__-data-update
  APPLICATION_JMS_RESULT_READY_ENABLED: 'false'
  APPLICATION_JMS_RESULT_READY_QUEUE: __ENV_BRANCH__-upload-result-ready
  APPLICATION_JMS_SCAN_EVENT_ENABLED: 'true'
  APPLICATION_JMS_POLICY_EVENT_QUEUE: __ENV_BRANCH__-policy-event
  APPLICATION_JMS_POLICY_EVENT_ENABLED: 'true'
  APPLICATION_JMS_SCAN_UPDATE_EVENT_QUEUE: __ENV_BRANCH__-sca-scan-event-srm
  APPLICATION_JMS_SCAN_UPDATE_EVENT_ENABLED: 'true'
  APPLICATION_JMS_SNS_ENABLED: 'true'
  APPLICATION_SCHEDULED_JOB_ENABLED: 'true'
  APPLICATION_SERVICES_POLICY_BACKEND_URL: https://policy-backend.__ENV_BRANCH__
  AWS_SECRETSMANAGER_ENABLED: 'false'
  SSL_CLIENT_AUTH: 'want'
  KEY_STORE: 'sca-scanresult-service-keystore.jks'
  TRUST_STORE: 'truststore.jks'
  # SSL Configuration for Spring Boot
  SERVER_SSL_ENABLED: 'true'
  SERVER_SSL_KEY_STORE: '/data/config/sca-scanresult-service-keystore.jks'
  SERVER_SSL_KEY_STORE_TYPE: 'JKS'
  SERVER_SSL_TRUST_STORE: '/data/config/truststore.jks'
  SERVER_SSL_TRUST_STORE_TYPE: 'JKS'
  SERVER_PORT: '8509'
  SPRING_DATASOURCE_READWRITEDB_URL: ********************************************************************************************************************************
  SPRING_DATASOURCE_READWRITEDB_HIKARI_MAXIMUMPOOLSIZE: 40
  SPRING_DATASOURCE_READONLYDB_URL: ***********************************************************************************************************************************
  SPRING_DATASOURCE_READONLYDB_HIKARI_MAXIMUMPOOLSIZE: 40
  SPRING_REDIS_HOST: 'sca-redis-primary'
  SPRING_REDIS_PORT: '6379'
  SPRING_CACHE_TYPE: 'redis'
  PLATFORM_API_URL: http://platform-backend.__ENV_BRANCH__
  PLATFORM_SIGN_KEY_FILE: sca-scanresult-signing.pem
  REGISTRY_API_URL: http://registry-api.registry
  # Findings API Configurations
  FINDINGS_LEGACY_QUERY: 'false'
  REDIS_CACHEABLES_FINDINGS_EXPIRATION: '86400' # extending caching to 24hrs to maximize performance improvement
  REDIS_CACHEABLES_FINDINGS_WITH_NVD_EXPIRATION: '86400'
  # DataDog variables --------------------------------------------------------------------------
  DD_TAGS: "env:__ENV_NAME__ stack_name:__ENV_BRANCH__"
  DD_SERVICE: "sca-scanresult-service"
  DD_PROFILING_ENABLED: __DD_PROFILING_ENABLED__
  JAVA_TOOL_OPTIONS: "-javaagent:/datadog/dd-java-agent.jar"


valueFrom:
  - name: POD_NAMESPACE
    valueFrom:
      fieldRef:
        fieldPath: metadata.namespace

envFrom: |
  - secretRef:
      name: sca-scanresult-service-secrets

volumeMounts:
  - mountPath: "/data/config/agora-identityservice-principal-signing-1.pem"
    name: sca-scanresult-cert-mount
    subPath: agora-identityservice-principal-signing-1.pem
    readOnly: true
  - mountPath: "/data/config/sca-scanresult-signing.pem"
    name: sca-scanresult-cert-mount
    subPath: sca-scanresult-signing.pem
    readOnly: true
  - name: sca-scanresult-cert-mount
    mountPath: "/data/config/sca-scanresult-service-keystore.jks"
    subPath: sca-scanresult-service-keystore.jks
    readOnly: true
  - name: sca-scanresult-cert-mount
    mountPath: "/data/config/truststore.jks"
    subPath: truststore.jks
    readOnly: true

readinessProbe: |
  httpGet:
    path: /sca/scanresult/actuator/health
    port: actuator
  initialDelaySeconds: 90
livenessProbe: |
  httpGet:
    path: /sca/scanresult/actuator/health
    port: actuator
  initialDelaySeconds: 90

resources:
  requests:
    cpu: 256m
    memory: 512Mi
  limits:
    memory: 1536Mi

volumes:
  - name: sca-scanresult-cert-mount
    secret:
      defaultMode: 420
      secretName: sca-scanresult-service-certs 

automountServiceAccountToken: true

progressDeadlineSeconds: 180

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0
    
command: "[java]"
args: [-jar, /data/sca-scanresult-service.jar]

ingress:
  enabled: false
  tls: []

alb_ingress:
  enabled: false

virtualService:
  enabled: false

internalIngress: {}

persistence:
  enabled: false
  accessMode: ReadWriteOnce
  size: 1Gi

nodeSelector: {}
tolerations: []
affinity: {}

cronjob:
  enabled: false

job:
  enabled: false
