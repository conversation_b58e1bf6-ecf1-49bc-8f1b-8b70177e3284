apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "scanresult-service.fullname" . }}-test-connection"
  labels:
{{ include "scanresult-service.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "scanresult-service.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never

