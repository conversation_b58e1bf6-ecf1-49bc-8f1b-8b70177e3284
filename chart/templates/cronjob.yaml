{{- if and .Values.cronjob .Values.cronjob.enabled }}
{{- $cn := include "scanresult-service.fullname" . -}}
{{- $cjName := .Values.cronjob.name | default $cn }} # the default configmap name can be overriden with the .name property
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $cjName }}
  labels:
{{ include "scanresult-service.labels" . | indent 4 }}
  annotations:
{{ toYaml .Values.cronjob.annotations | indent 4 }}
spec:
  schedule: "{{ .Values.cronjob.schedule }}"
  concurrencyPolicy: Forbid
  {{- with .Values.cronjob.failedJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ . }}
  {{- end }}
  {{- with .Values.cronjob.successfulJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ . }}
  {{- end }}
  jobTemplate:
    metadata:
      labels:
        app.kubernetes.io/name: {{ $cjName }}
        app.kubernetes.io/managed-by: {{ .Release.Service }}
    spec:
      template:
        metadata:
          labels:
            app.kubernetes.io/name: {{ $cjName }}
            app.kubernetes.io/managed-by: {{ .Release.Service }}
        spec:
          restartPolicy: Never
          {{- if (default .Values.image.pullSecrets .Values.cronjob.image.pullSecrets) }}
          imagePullSecrets:
          {{- range (default .Values.image.pullSecrets .Values.cronjob.image.pullSecrets) }}
            - name: {{ . }}
          {{- end }}
          {{- end }}
          {{- if .Values.serviceAccount }}
          serviceAccountName: {{ .Values.serviceAccount.name | default .Values.app }}
          {{- end }}
          containers:
            - name: {{ include "scanresult-service.name" . }}
              image: "{{ default .Values.image.repository .Values.cronjob.image.repository }}:{{ default .Values.image.tag .Values.cronjob.image.tag }}"
              imagePullPolicy: {{ default .Values.image.pullPolicy .Values.cronjob.image.pullPolicy }}
              {{- with .Values.cronjob.command }} 
              # Command to be executed instead of the default entrypoint script (will replace helm variables)
              command:
{{ tpl . $ | indent 16 }}
              {{- end }}   
              {{- with .Values.cronjob.args }} # Adding arguments for the entrypoint script
              args:
                {{- toYaml . | nindent 16 }}
              {{- end }}    
        {{- with .Values.cronjob.env }}
              env:
                {{ toYaml . | nindent 16 }} # Adds environment variables, also replacing helm variables (if found in definition)
        {{- end }}          
              resources:
{{ toYaml (default .Values.resources .Values.cronjob.resources) | indent 16 }}
    {{- with (default .Values.nodeSelector .Values.cronjob.nodeSelector) }}
          nodeSelector:
{{ toYaml . | indent 12 }}
    {{- end }}
    {{- with (default .Values.affinity .Values.cronjob.affinity) }}
          affinity:
{{ toYaml . | indent 12 }}
    {{- end }}
    {{- with (default .Values.tolerations .Values.cronjob.tolerations) }}
          tolerations:
{{ toYaml . | indent 12 }}:
    {{- end }}
{{- end }}
