{{- if .Values.StatefulSet }} # Define a statefulset instead of the default Deployment
{{- $cn := include "scanresult-service.fullname" . -}}
{{- $cName := .Values.containerName | default $cn}} 
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "scanresult-service.fullname" . }}
  labels:
{{ include "scanresult-service.labels" . | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount | default 1}}
  podManagementPolicy: {{ .Values.podManagementPolicy | default "OrderedReady" }} # Specific to statefulsets
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "scanresult-service.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app: {{ .Values.app | default "o2"  }}
      name: {{ include "scanresult-service.name" . }}      
  serviceName: {{ .Values.app }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "scanresult-service.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app: {{ .Values.app | default "o2" }}
        name: {{ include "scanresult-service.name" . }}
        {{- if .Values.podLabels }}
        {{- toYaml .Values.podLabels | nindent 8 }}
        {{- end }}
    {{- with .Values.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}   
    spec:
    {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
    {{- end }}
        {{- with .Values.initContainers }}
      initContainers:
{{ tpl . $ | indent 8 }}
        {{- end }}
      containers:
        {{- with .Values.extraContainers }}
{{ tpl . $ | indent 8 }}
        {{- end }}        
        - name: {{ $cName }}       
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.args }}
          args:
            {{- toYaml . | nindent 12 }}
          {{- end }}           
          {{- with .Values.command }}
          command:
            {{- toYaml . | nindent 12 }}
          {{- end }}          
          ports:
{{- if .Values.ports }}
  {{- with .Values.ports }}
{{ tpl . $ | indent 12 }}
  {{- end }}
{{- else }}
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
{{- end }}
    {{- with .Values.env }}
          env:
{{ tpl . $ | indent 12 }} #in theory it allows a post evaluation of variables inside values.yaml
    {{- end }}
    {{- with .Values.livenessProbe }}
          livenessProbe:
{{ tpl . $ | indent 12 }}
    {{- end }}
    {{- with .Values.readinessProbe }}
          readinessProbe:
{{ tpl . $ | indent 12 }}
    {{- end }}
    {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
    {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
    {{- end }}
  {{- if .Values.serviceAccount }}
      serviceAccount: {{ .Values.serviceAccount.name | default .Values.app }}
      serviceAccountName: {{ .Values.serviceAccount.name | default .Values.app }}
  {{- end }}  
  {{- with .Values.volumeClaimTemplates }}  
  volumeClaimTemplates:
    {{- toYaml . | nindent 4 }}
  {{- end }}  
{{- end }}

