{{- if .Values.ingress.enabled -}}
{{- $fullName := include "scanresult-service.fullname" . -}}
{{- $targetPort := .Values.service.targetPort | default "http" }} # if no targetPort is defined, it defaults to http
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
{{ include "scanresult-service.labels" . | indent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className }}
{{- end }}
{{- if .Values.ingress.tls }} # if defined it can handle ssl connection
  tls:
  {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.ingress.hosts }} # multiple hosts can be defined
    - host: {{ .host | quote }}
      http:
        paths:
        {{- range .paths }} # multiple paths can be added, if needed
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ .service | default $fullName }}
                port:
                  name: {{ .port | default $targetPort }}
        {{- end }}
  {{- end }}
{{- end }}

